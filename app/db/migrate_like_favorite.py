"""
数据库迁移脚本：创建点赞和收藏表
"""

import logging
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.db.session import engine

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_likes_table(db: Session) -> None:
    """创建点赞表"""
    create_likes_sql = """
    CREATE TABLE IF NOT EXISTS likes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        content_type VARCHAR(20) NOT NULL,
        content_id INTEGER NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE (user_id, content_type, content_id)
    );
    """
    
    # 创建索引
    create_likes_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_likes_user_id ON likes (user_id);",
        "CREATE INDEX IF NOT EXISTS idx_likes_content_type ON likes (content_type);",
        "CREATE INDEX IF NOT EXISTS idx_likes_content_id ON likes (content_id);",
        "CREATE INDEX IF NOT EXISTS idx_likes_is_active ON likes (is_active);",
        "CREATE INDEX IF NOT EXISTS idx_likes_created_at ON likes (created_at);",
        "CREATE INDEX IF NOT EXISTS idx_likes_content_type_id ON likes (content_type, content_id);",
    ]
    
    try:
        db.execute(text(create_likes_sql))
        logger.info("点赞表创建成功")
        
        for index_sql in create_likes_indexes_sql:
            db.execute(text(index_sql))
        logger.info("点赞表索引创建成功")
        
        db.commit()
    except Exception as e:
        logger.error(f"创建点赞表失败: {e}")
        db.rollback()
        raise


def create_favorites_table(db: Session) -> None:
    """创建收藏表"""
    create_favorites_sql = """
    CREATE TABLE IF NOT EXISTS favorites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        content_type VARCHAR(20) NOT NULL,
        content_id INTEGER NOT NULL,
        note TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        UNIQUE (user_id, content_type, content_id)
    );
    """
    
    # 创建索引
    create_favorites_indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON favorites (user_id);",
        "CREATE INDEX IF NOT EXISTS idx_favorites_content_type ON favorites (content_type);",
        "CREATE INDEX IF NOT EXISTS idx_favorites_content_id ON favorites (content_id);",
        "CREATE INDEX IF NOT EXISTS idx_favorites_is_active ON favorites (is_active);",
        "CREATE INDEX IF NOT EXISTS idx_favorites_created_at ON favorites (created_at);",
        "CREATE INDEX IF NOT EXISTS idx_favorites_content_type_id ON favorites (content_type, content_id);",
    ]
    
    try:
        db.execute(text(create_favorites_sql))
        logger.info("收藏表创建成功")
        
        for index_sql in create_favorites_indexes_sql:
            db.execute(text(index_sql))
        logger.info("收藏表索引创建成功")
        
        db.commit()
    except Exception as e:
        logger.error(f"创建收藏表失败: {e}")
        db.rollback()
        raise


def check_table_exists(db: Session, table_name: str) -> bool:
    """检查表是否存在"""
    check_sql = """
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name=:table_name;
    """
    result = db.execute(text(check_sql), {"table_name": table_name}).fetchone()
    return result is not None


def migrate_like_favorite_tables() -> None:
    """执行点赞和收藏表的迁移"""
    from sqlalchemy.orm import sessionmaker
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        logger.info("开始创建点赞和收藏表...")
        
        # 检查表是否已存在
        if check_table_exists(db, "likes"):
            logger.info("点赞表已存在，跳过创建")
        else:
            create_likes_table(db)
            
        if check_table_exists(db, "favorites"):
            logger.info("收藏表已存在，跳过创建")
        else:
            create_favorites_table(db)
            
        logger.info("点赞和收藏表迁移完成")
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        raise
    finally:
        db.close()


def create_sample_data(db: Session) -> None:
    """创建示例数据（可选）"""
    # 这里可以添加一些示例的点赞和收藏数据
    # 但通常在生产环境中不需要
    pass


if __name__ == "__main__":
    # 直接运行此脚本来执行迁移
    migrate_like_favorite_tables()
    logger.info("迁移脚本执行完成")
