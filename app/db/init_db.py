import logging

from sqlalchemy.orm import Session

from app.db.init_permissions import init_permissions
from app.db.session import Base, engine

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db(db: Session) -> None:
    """初始化数据库"""
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    logger.info("数据库表已创建")

    # 执行点赞和收藏表迁移
    try:
        from app.db.migrate_like_favorite import migrate_like_favorite_tables

        migrate_like_favorite_tables()
        logger.info("点赞和收藏表迁移完成")
    except Exception as e:
        logger.error(f"点赞和收藏表迁移失败: {e}")

    # 初始化权限和角色
    init_permissions(db)


def init_test_data(db: Session) -> None:
    """初始化测试数据"""
    # 检查是否已有游戏数据
    game = db.query(Game).first()
    if game:
        logger.info("数据库中已有测试数据")
        return

    # 添加一些测试游戏数据
    test_games = [
        Game(
            steam_appid=570,
            name="Dota 2",
            description="每一天，全球有数百万玩家化身为上百位Dota英雄展开战斗。无论是游戏时间刚满10小时还是1000小时，战场上总有新鲜事物等着您发现。定期推出的游戏更新则保证了英雄和技能的变化，让游戏体验始终充满活力。",
            developer="Valve",
            publisher="Valve",
            price=0.0,  # 免费游戏
            image_url="https://cdn.akamai.steamstatic.com/steam/apps/570/header.jpg",
        ),
        Game(
            steam_appid=730,
            name="Counter-Strike 2",
            description="Counter-Strike 2 是 CS:GO 的免费升级版，它采用了 Source 2 引擎打造，拥有更真实的视觉效果、更精准的物理模拟和更先进的网络技术。",
            developer="Valve",
            publisher="Valve",
            price=0.0,  # 免费游戏
            image_url="https://cdn.akamai.steamstatic.com/steam/apps/730/header.jpg",
        ),
        Game(
            steam_appid=1172470,
            name="Apex Legends",
            description="Apex Legends是一款免费游玩的英雄射击大逃杀游戏。在不断变化的世界中，掌握不断增长的传奇角色名册，深度战术小队游戏，以及游戏改变的创新。",
            developer="Respawn Entertainment",
            publisher="Electronic Arts",
            price=0.0,  # 免费游戏
            image_url="https://cdn.akamai.steamstatic.com/steam/apps/1172470/header.jpg",
        ),
    ]

    # 添加到数据库
    for game in test_games:
        db.add(game)

    db.commit()
    logger.info(f"已添加 {len(test_games)} 个测试游戏数据")
