from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.post("/", response_model=schemas.Article)
def create_article(
    *,
    db: Session = Depends(deps.get_db),
    article_in: schemas.ArticleCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新文章"""
    article = crud.article.create(db=db, obj_in=article_in)
    # 创建审核记录
    review_in = schemas.ReviewCreate(
        content_type="article",
        content_id=article.id,
    )
    crud.review.create(db=db, obj_in=review_in)
    return article


@router.get("/", response_model=schemas.ArticleList)
def read_articles(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取文章列表"""
    # 管理员可以看到所有文章，普通用户只能看到已发布的文章
    if crud.user.is_admin(current_user):
        articles = crud.article.get_multi(db, skip=skip, limit=limit)
        total = db.query(models.Article).count()
    else:
        articles = crud.article.get_published(db, skip=skip, limit=limit)
        total = (
            db.query(models.Article).filter(models.Article.is_published == True).count()
        )
    return {"total": total, "items": articles}


@router.get("/my", response_model=schemas.ArticleList)
def read_my_articles(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的文章列表"""
    articles = crud.article.get_multi_by_author(
        db=db, author_id=current_user.id, skip=skip, limit=limit
    )
    total = (
        db.query(models.Article)
        .filter(models.Article.author_id == current_user.id)
        .count()
    )
    return {"total": total, "items": articles}


@router.get("/{article_id}", response_model=schemas.Article)
def read_article(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取文章详情"""
    article = crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 非管理员且非作者只能查看已发布的文章
    if (
        not article.is_published
        and not crud.user.is_admin(current_user)
        and article.author_id != current_user.id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该文章",
        )
    return article


@router.put("/{article_id}", response_model=schemas.Article)
def update_article(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    article_in: schemas.ArticleUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新文章"""
    article = crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 只有管理员和作者可以更新文章
    if not crud.user.is_admin(current_user) and article.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新该文章",
        )
    # 如果内容有更新，需要重新审核
    if article_in.title or article_in.content:
        article_in_dict = article_in.dict(exclude_unset=True)
        article_in_dict["is_published"] = False
        article = crud.article.update(db=db, db_obj=article, obj_in=article_in_dict)
        # 创建新的审核记录
        review = crud.review.get_by_content(
            db, content_type="article", content_id=article.id
        )
        if review:
            crud.review.update(
                db=db,
                db_obj=review,
                obj_in={"status": "pending", "reviewer_id": None, "reviewed_at": None},
            )
        else:
            review_in = schemas.ReviewCreate(
                content_type="article",
                content_id=article.id,
            )
            crud.review.create(db=db, obj_in=review_in)
    else:
        article = crud.article.update(db=db, db_obj=article, obj_in=article_in)
    return article


@router.delete("/{article_id}", response_model=schemas.Article)
def delete_article(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """删除文章"""
    article = crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 只有管理员和作者可以删除文章
    if not crud.user.is_admin(current_user) and article.author_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除该文章",
        )
    # 删除关联的审核记录
    review = crud.review.get_by_content(
        db, content_type="article", content_id=article.id
    )
    if review:
        crud.review.remove(db=db, id=review.id)
    article = crud.article.remove(db=db, id=article_id)
    return article


@router.get("/with-stats", response_model=schemas.ArticleListWithStats)
def read_articles_with_stats(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取包含统计信息的文章列表"""
    from app.services.favorite_cache_service import favorite_cache_service
    from app.services.like_cache_service import like_cache_service

    # 管理员可以看到所有文章，普通用户只能看到已发布的文章
    if crud.user.is_admin(current_user):
        articles = crud.article.get_multi(db, skip=skip, limit=limit)
        total = db.query(models.Article).count()
    else:
        articles = crud.article.get_published(db, skip=skip, limit=limit)
        total = (
            db.query(models.Article).filter(models.Article.is_published == True).count()
        )

    # 批量获取点赞和收藏信息
    content_items = [("article", article.id) for article in articles]

    like_info = like_cache_service.batch_get_like_info(content_items, current_user.id)
    favorite_info = favorite_cache_service.batch_get_favorite_info(
        content_items, current_user.id
    )

    # 从数据库获取缺失的数据
    missing_like_items = []
    missing_favorite_items = []

    for item_key in content_items:
        like_data = like_info.get(item_key, {})
        if like_data.get("like_count") is None or like_data.get("is_liked") is None:
            missing_like_items.append(item_key)

        favorite_data = favorite_info.get(item_key, {})
        if (
            favorite_data.get("favorite_count") is None
            or favorite_data.get("is_favorited") is None
        ):
            missing_favorite_items.append(item_key)

    if missing_like_items:
        db_like_info = crud.like.get_content_likes_batch(
            db, content_items=missing_like_items, user_id=current_user.id
        )
        like_info.update(db_like_info)

    if missing_favorite_items:
        db_favorite_info = crud.favorite.get_content_favorites_batch(
            db, content_items=missing_favorite_items, user_id=current_user.id
        )
        favorite_info.update(db_favorite_info)

    # 构建响应
    articles_with_stats = []
    for article in articles:
        item_key = ("article", article.id)
        like_data = like_info.get(item_key, {})
        favorite_data = favorite_info.get(item_key, {})

        article_dict = {
            **article.__dict__,
            "like_count": like_data.get("like_count", 0),
            "favorite_count": favorite_data.get("favorite_count", 0),
            "is_liked_by_user": like_data.get("is_liked", False),
            "is_favorited_by_user": favorite_data.get("is_favorited", False),
        }
        articles_with_stats.append(schemas.ArticleWithStats(**article_dict))

    return {"total": total, "items": articles_with_stats}


@router.get("/{article_id}/with-stats", response_model=schemas.ArticleWithStats)
def read_article_with_stats(
    *,
    db: Session = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取包含统计信息的文章详情"""
    from app.services.favorite_cache_service import favorite_cache_service
    from app.services.like_cache_service import like_cache_service

    article = crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 非管理员且非作者只能查看已发布的文章
    if (
        not article.is_published
        and not crud.user.is_admin(current_user)
        and article.author_id != current_user.id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该文章",
        )

    # 获取点赞和收藏信息
    like_count = like_cache_service.get_like_count("article", article_id)
    if like_count is None:
        like_count = crud.like.get_content_like_count(
            db, content_type="article", content_id=article_id
        )
        like_cache_service.set_like_count("article", article_id, like_count)

    favorite_count = favorite_cache_service.get_favorite_count("article", article_id)
    if favorite_count is None:
        favorite_count = crud.favorite.get_content_favorite_count(
            db, content_type="article", content_id=article_id
        )
        favorite_cache_service.set_favorite_count("article", article_id, favorite_count)

    is_liked = like_cache_service.is_liked_by_user(
        current_user.id, "article", article_id
    )
    if is_liked is None:
        is_liked = crud.like.is_liked_by_user(
            db, user_id=current_user.id, content_type="article", content_id=article_id
        )
        like_cache_service.set_user_like_status(
            current_user.id, "article", article_id, is_liked
        )

    is_favorited = favorite_cache_service.is_favorited_by_user(
        current_user.id, "article", article_id
    )
    if is_favorited is None:
        is_favorited = crud.favorite.is_favorited_by_user(
            db, user_id=current_user.id, content_type="article", content_id=article_id
        )
        favorite_cache_service.set_user_favorite_status(
            current_user.id, "article", article_id, is_favorited
        )

    # 构建响应
    article_dict = {
        **article.__dict__,
        "like_count": like_count,
        "favorite_count": favorite_count,
        "is_liked_by_user": is_liked,
        "is_favorited_by_user": is_favorited,
    }

    return schemas.ArticleWithStats(**article_dict)
