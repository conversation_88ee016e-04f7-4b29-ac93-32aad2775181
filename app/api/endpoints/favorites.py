from typing import Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.services.favorite_cache_service import favorite_cache_service

router = APIRouter()


@router.post("/toggle", response_model=schemas.FavoriteStatus)
def toggle_favorite(
    *,
    db: Session = Depends(deps.get_db),
    favorite_data: schemas.FavoriteToggle,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """切换收藏状态"""
    # 验证内容是否存在
    if favorite_data.content_type == "article":
        content = crud.article.get(db, id=favorite_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )
    elif favorite_data.content_type == "video":
        content = crud.video.get(db, id=favorite_data.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 切换收藏状态
    favorite_obj, is_favorited = crud.favorite.toggle_favorite(
        db,
        user_id=current_user.id,
        content_type=favorite_data.content_type,
        content_id=favorite_data.content_id,
        note=favorite_data.note,
    )

    # 更新缓存
    if is_favorited:
        favorite_cache_service.increment_favorite_count(favorite_data.content_type, favorite_data.content_id)
    else:
        favorite_cache_service.decrement_favorite_count(favorite_data.content_type, favorite_data.content_id)
    
    favorite_cache_service.set_user_favorite_status(
        current_user.id, favorite_data.content_type, favorite_data.content_id, is_favorited
    )

    # 获取最新的收藏数量
    favorite_count = crud.favorite.get_content_favorite_count(
        db, content_type=favorite_data.content_type, content_id=favorite_data.content_id
    )

    return schemas.FavoriteStatus(
        content_type=favorite_data.content_type,
        content_id=favorite_data.content_id,
        is_favorited=is_favorited,
        favorite_count=favorite_count,
    )


@router.get("/status", response_model=schemas.FavoriteStatus)
def get_favorite_status(
    *,
    db: Session = Depends(deps.get_db),
    content_type: str = Query(..., description="内容类型"),
    content_id: int = Query(..., description="内容ID"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取收藏状态"""
    # 先尝试从缓存获取
    cached_count = favorite_cache_service.get_favorite_count(content_type, content_id)
    cached_status = favorite_cache_service.is_favorited_by_user(current_user.id, content_type, content_id)

    if cached_count is not None and cached_status is not None:
        return schemas.FavoriteStatus(
            content_type=content_type,
            content_id=content_id,
            is_favorited=cached_status,
            favorite_count=cached_count,
        )

    # 从数据库获取
    favorite_count = crud.favorite.get_content_favorite_count(
        db, content_type=content_type, content_id=content_id
    )
    is_favorited = crud.favorite.is_favorited_by_user(
        db, user_id=current_user.id, content_type=content_type, content_id=content_id
    )

    # 更新缓存
    favorite_cache_service.set_favorite_count(content_type, content_id, favorite_count)
    favorite_cache_service.set_user_favorite_status(current_user.id, content_type, content_id, is_favorited)

    return schemas.FavoriteStatus(
        content_type=content_type,
        content_id=content_id,
        is_favorited=is_favorited,
        favorite_count=favorite_count,
    )


@router.get("/history", response_model=schemas.FavoriteHistory)
def get_favorite_history(
    *,
    db: Session = Depends(deps.get_db),
    content_type: Optional[str] = Query(None, description="内容类型筛选"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户收藏历史"""
    favorites = crud.favorite.get_user_favorites(
        db, user_id=current_user.id, content_type=content_type, skip=skip, limit=limit
    )
    total = crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type=content_type
    )

    return schemas.FavoriteHistory(total=total, items=favorites)


@router.put("/{favorite_id}/note", response_model=schemas.Favorite)
def update_favorite_note(
    *,
    db: Session = Depends(deps.get_db),
    favorite_id: int,
    note_data: schemas.FavoriteUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新收藏备注"""
    favorite = crud.favorite.get(db, id=favorite_id)
    if not favorite:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="收藏记录不存在",
        )
    
    if favorite.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限修改此收藏记录",
        )

    updated_favorite = crud.favorite.update(db=db, db_obj=favorite, obj_in=note_data)
    return updated_favorite


@router.get("/stats", response_model=schemas.FavoriteStats)
def get_favorite_stats(
    *,
    db: Session = Depends(deps.get_db),
    content_type: Optional[str] = Query(None, description="内容类型筛选"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取收藏统计信息（仅管理员）"""
    if not crud.user.is_admin(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问统计信息",
        )

    stats = crud.favorite.get_favorite_stats(db, content_type=content_type)
    return schemas.FavoriteStats(**stats)


@router.post("/batch-status", response_model=list[schemas.ContentFavoriteInfo])
def get_batch_favorite_status(
    *,
    db: Session = Depends(deps.get_db),
    content_items: list[schemas.FavoriteBase],
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """批量获取内容收藏信息"""
    if not content_items:
        return []

    # 转换为元组列表
    items = [(item.content_type, item.content_id) for item in content_items]

    # 先尝试从缓存批量获取
    cached_info = favorite_cache_service.batch_get_favorite_info(items, current_user.id)

    # 检查哪些需要从数据库获取
    missing_items = []
    for item_key in items:
        cache_data = cached_info.get(item_key, {})
        if cache_data.get("favorite_count") is None or cache_data.get("is_favorited") is None:
            missing_items.append(item_key)

    # 从数据库获取缺失的数据
    if missing_items:
        db_info = crud.favorite.get_content_favorites_batch(db, content_items=missing_items, user_id=current_user.id)
        
        # 更新缓存
        for item_key, data in db_info.items():
            content_type, content_id = item_key
            favorite_cache_service.set_favorite_count(content_type, content_id, data["favorite_count"])
            favorite_cache_service.set_user_favorite_status(
                current_user.id, content_type, content_id, data["is_favorited"]
            )
        
        # 合并数据
        cached_info.update(db_info)

    # 构建响应
    result = []
    for content_type, content_id in items:
        data = cached_info.get((content_type, content_id), {})
        result.append(
            schemas.ContentFavoriteInfo(
                content_type=content_type,
                content_id=content_id,
                favorite_count=data.get("favorite_count", 0),
                is_favorited_by_user=data.get("is_favorited", False),
            )
        )

    return result
