from fastapi import APIRouter

from app.api.endpoints import (
    articles,
    auth,
    comments,
    favorites,
    likes,
    reviews,
    roles,
    user_history,
    users,
    validation_demo,
    videos,
)

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(articles.router, prefix="/articles", tags=["articles"])
api_router.include_router(videos.router, prefix="/videos", tags=["videos"])
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])
api_router.include_router(comments.router, prefix="/comments", tags=["comments"])
api_router.include_router(likes.router, prefix="/likes", tags=["likes"])
api_router.include_router(favorites.router, prefix="/favorites", tags=["favorites"])
api_router.include_router(
    user_history.router, prefix="/user/history", tags=["user-history"]
)
api_router.include_router(
    validation_demo.router, prefix="/validation", tags=["validation-demo"]
)
