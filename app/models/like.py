from datetime import datetime

from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship

from app.db.session import Base


class Like(Base):
    """点赞数据模型"""

    __tablename__ = "likes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    
    # 内容类型：article 或 video
    content_type = Column(String(20), nullable=False, index=True)
    
    # 内容ID（文章ID或视频ID）
    content_id = Column(Integer, nullable=False, index=True)
    
    # 是否有效（用于软删除）
    is_active = Column(Boolean, default=True, index=True)
    
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    user = relationship("User")

    # 联合唯一约束：同一用户对同一内容只能点赞一次
    __table_args__ = (
        UniqueConstraint('user_id', 'content_type', 'content_id', name='uq_user_content_like'),
    )

    def __repr__(self):
        return f"<Like user_id={self.user_id} {self.content_type}:{self.content_id}>"
