from datetime import datetime, timed<PERSON>ta
from typing import Any, Optional

from sqlalchemy import and_, func
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.favorite import Favorite
from app.schemas.favorite import FavoriteCreate, FavoriteUpdate


class CRUDFavorite(CRUDBase[Favorite, FavoriteCreate, FavoriteUpdate]):
    """收藏CRUD操作"""

    def get_by_user_and_content(
        self, db: Session, *, user_id: int, content_type: str, content_id: int
    ) -> Optional[Favorite]:
        """根据用户和内容获取收藏记录"""
        return (
            db.query(self.model)
            .filter(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )
            .first()
        )

    def toggle_favorite(
        self, 
        db: Session, 
        *, 
        user_id: int, 
        content_type: str, 
        content_id: int,
        note: Optional[str] = None
    ) -> tuple[Favorite, bool]:
        """切换收藏状态
        
        Returns:
            tuple: (Favorite对象, 是否为新增收藏)
        """
        existing_favorite = self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )
        
        if existing_favorite:
            # 切换激活状态
            existing_favorite.is_active = not existing_favorite.is_active
            existing_favorite.updated_at = datetime.utcnow()
            if note is not None:
                existing_favorite.note = note
            db.add(existing_favorite)
            db.commit()
            db.refresh(existing_favorite)
            return existing_favorite, existing_favorite.is_active
        else:
            # 创建新的收藏记录
            favorite_data = {
                "user_id": user_id,
                "content_type": content_type,
                "content_id": content_id,
                "note": note,
                "is_active": True,
            }
            new_favorite = self.model(**favorite_data)
            db.add(new_favorite)
            db.commit()
            db.refresh(new_favorite)
            return new_favorite, True

    def get_content_favorite_count(
        self, db: Session, *, content_type: str, content_id: int
    ) -> int:
        """获取内容的收藏数量"""
        return (
            db.query(self.model)
            .filter(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                    self.model.is_active == True,
                )
            )
            .count()
        )

    def is_favorited_by_user(
        self, db: Session, *, user_id: int, content_type: str, content_id: int
    ) -> bool:
        """检查用户是否已收藏"""
        favorite = self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )
        return favorite is not None and favorite.is_active

    def get_user_favorites(
        self,
        db: Session,
        *,
        user_id: int,
        content_type: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[Favorite]:
        """获取用户的收藏历史"""
        query = db.query(self.model).filter(
            and_(self.model.user_id == user_id, self.model.is_active == True)
        )
        
        if content_type:
            query = query.filter(self.model.content_type == content_type)
            
        return (
            query.order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_user_favorite_count(
        self, db: Session, *, user_id: int, content_type: Optional[str] = None
    ) -> int:
        """获取用户的收藏总数"""
        query = db.query(self.model).filter(
            and_(self.model.user_id == user_id, self.model.is_active == True)
        )
        
        if content_type:
            query = query.filter(self.model.content_type == content_type)
            
        return query.count()

    def get_favorite_stats(
        self, db: Session, *, content_type: Optional[str] = None
    ) -> dict[str, int]:
        """获取收藏统计信息"""
        now = datetime.utcnow()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        week_start = today_start - timedelta(days=now.weekday())
        month_start = today_start.replace(day=1)

        base_query = db.query(self.model).filter(self.model.is_active == True)
        
        if content_type:
            base_query = base_query.filter(self.model.content_type == content_type)

        total = base_query.count()
        today = base_query.filter(self.model.created_at >= today_start).count()
        this_week = base_query.filter(self.model.created_at >= week_start).count()
        this_month = base_query.filter(self.model.created_at >= month_start).count()

        return {
            "total_favorites": total,
            "today_favorites": today,
            "this_week_favorites": this_week,
            "this_month_favorites": this_month,
        }

    def get_content_favorites_batch(
        self, db: Session, *, content_items: list[tuple[str, int]], user_id: Optional[int] = None
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """批量获取内容的收藏信息
        
        Args:
            content_items: [(content_type, content_id), ...] 列表
            user_id: 可选的用户ID，用于检查用户是否已收藏
            
        Returns:
            {(content_type, content_id): {"favorite_count": int, "is_favorited": bool}}
        """
        if not content_items:
            return {}

        # 构建查询条件
        conditions = []
        for content_type, content_id in content_items:
            conditions.append(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )

        # 获取收藏统计
        favorite_counts = (
            db.query(
                self.model.content_type,
                self.model.content_id,
                func.count(self.model.id).label("favorite_count"),
            )
            .filter(
                and_(
                    self.model.is_active == True,
                    func.or_(*conditions),
                )
            )
            .group_by(self.model.content_type, self.model.content_id)
            .all()
        )

        # 构建结果字典
        result = {}
        for content_type, content_id in content_items:
            result[(content_type, content_id)] = {
                "favorite_count": 0,
                "is_favorited": False,
            }

        # 填充收藏数量
        for content_type, content_id, favorite_count in favorite_counts:
            result[(content_type, content_id)]["favorite_count"] = favorite_count

        # 如果提供了用户ID，检查用户收藏状态
        if user_id:
            user_favorites = (
                db.query(self.model.content_type, self.model.content_id)
                .filter(
                    and_(
                        self.model.user_id == user_id,
                        self.model.is_active == True,
                        func.or_(*conditions),
                    )
                )
                .all()
            )
            
            for content_type, content_id in user_favorites:
                if (content_type, content_id) in result:
                    result[(content_type, content_id)]["is_favorited"] = True

        return result

    def update_favorite_note(
        self, db: Session, *, user_id: int, content_type: str, content_id: int, note: str
    ) -> Optional[Favorite]:
        """更新收藏备注"""
        favorite = self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )
        
        if favorite and favorite.is_active:
            favorite.note = note
            favorite.updated_at = datetime.utcnow()
            db.add(favorite)
            db.commit()
            db.refresh(favorite)
            return favorite
        
        return None


favorite = CRUDFavorite(Favorite)
